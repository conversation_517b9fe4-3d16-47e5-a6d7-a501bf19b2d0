/* body {
    font-family: 'Poppins', sans-serif;
    background: #cad1ff;
    background-size: cover;
    background-origin: border-box;
    background-position: center;
    background-attachment: fixed;
    min-height: 100vh;
    align-items: center;
    justify-content: center;
    overflow: hidden;
} */

* {
  list-style: none;
  text-decoration: none;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* font-family: 'Open Sans', sans-serif; */
}

body.active .wrapper .section {
  margin-left: 0;
  width: 100%;
}
*/

/* POST JOB FORM */
.headingtwo {
  display: flex;
  justify-content: center;
  padding-top: 5px;
  /*changed*/
  /* margin: 60px; */
  text-align: center;
  color: #000000;
  /* margin-top: 19px; */
  /*changed*/
  font-size: 18px;
  font-weight: 700;
  position: sticky;
  /* margin-bottom: 23px; */
  margin-left: 4%;
}

.jobassigncont {
  position: relative;

  width: 98%;
  /* margin-top: -22px;  */
  padding: 10px 10px;
  border-radius: 10px;
  border: 1px solid rgba(4, 4, 4, 0.18);
  /* height: 80vh; */
  margin-left: 12px;
  overflow: hidden;
  flex: 1;
}

.forms_JA {
  background-color: #fff;
  transition: 0.3s ease;
  padding: 5px;
  height: 100%;
  overflow: auto;
  /* overflow: auto; */
  /* 
  height:auto; */
  /* Adjust the height as needed */
}

.group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  column-gap: 15px;
  /*changed*/
  height: 70vh;
  row-gap: 12px;
  /*changed*/
}

.JS {
  display: flex;
  flex-direction: column;
  flex: 1 0 33.33%;
}

.JS label {
  font-size: 13px;
  /*changed*/
  font-weight: 480;
  color: #2e2e2e;
  margin-bottom: 0.1rem;
  /*changed*/
}

.JS input,
select,
textarea {
  outline: none;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  border-radius: 5px;
  border: 1px solid #aaa;
  padding: 3px 15px;
  /* height: 42px; */
  height: 30px;
  /* margin: 4px 0;  */
}

.JS input:focus,
.JS select:focus {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.13);
}

.JS select {
  color: #333;
}

input[type="date"],
input[type="file"],
input[type="checkbox"] {
  color: #707070;
}

.JS input[type="date"]:valid {
  color: #333;
}

.buttons_addjob {
  width: 100%;
  height: 35px;
  margin-top: 10px;
  /* background-color: red; */
  display: flex;
  align-items: center;
  justify-content: center;
  /* height: auto; */
}

#submits_addjob {
  /* display: flex; */
  /* align-items: center; */
  /* justify-content: center; */
  height: 35px;
  max-width: 160px;
  margin: 0 auto;

  /* width: 100%; */
  /* padding: 3px; */
  border: none;
  outline: none;
  color: #fff;
  border-radius: 5px;
  background-color: #32406d;
  transition: all 0.3s linear;
  cursor: pointer;
  /* margin-top: 10px; */
}

/* input[type='submit']:hover {
  background-color: #555;
} */

.error-message {
  color: #ff0000;
}

.required-field {
  color: red;
  margin-right: 4px;
}

/* .message {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 10px;
      height: 30px;
  }

  .success {
      background-color: #c1e2b3;
      color: #1b5e20;
  }

  .error {
      background-color: #ffb3b3;
      color: #c62828;
  } */

/* For desktop screens */
/* @media screen and (min-width: 1024px) {
  .container {
      overflow: auto;
  }

  .forms {
      height: 522px;
  }


} */

/* For tablet screens */
/* @media screen and (max-width: 1023px) and (min-width: 768px) {
  .container {
      overflow: auto;
  }


} */

/* For mobile screens */
@media screen and (max-width: 767px) {
  .JS {
    /* width: calc(100% / 2 - 15px); */
    flex-basis: 50%;
  }

  .h1 {
    font-size: 2px;
  }
}

@media (max-width: 550px) {
  .JS {
    flex-basis: 100%;
  }
  .logo {
    margin-left: 10px;
  }
}
.multiselect-dropdown-list {
  z-index: 1000;
  height: 5.6rem;
}
/* Form Validation styles */
.JS.error input,
.JS.error select,
.JS.error textarea {
  border-color: #e74c3c;
}

.JS small {
  color: #e74c3c;
  visibility: hidden;
}

.JS.error small {
  visibility: visible;
}

.JS.error .currency-input {
  border-color: #e74c3c;
}

.currency-input {
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 5px;
  overflow: hidden;
}

.currency-input select,
.currency-input input {
  border: none;
  padding: 3px;
  font-size: 12px;
  background-color: white;
}

.currency-input select {
  background-color: white;
  border-right: 1px solid #ccc;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  color: #555;
  cursor: pointer;
}

.currency-input input {
  flex: 1;
  padding-left: 5px;
}

#submit[disabled] {
  background-color: #cccccc;
  cursor: not-allowed;
}
@media only screen and (max-width: 768px) {
  .group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
}
/* Media query for mobile devices */
@media only screen and (max-width: 480px) {
  .group {
    display: flex;
    flex-direction: column;
  }
}

/* Media query for larger screens (PCs) */
@media only screen and (min-width: 1024px) {
}
@media only screen and (min-width: 1024px) {
  /* Adjust sidebar width for desktop */
  .sidebar {
    width: 250px; /* Adjust as needed */
  }

  /* Adjust section width to accommodate the sidebar */
  .section {
    margin-left: 250px; /* Same as sidebar width */
  }
}
body {
  font-family: Arial, sans-serif;
}

/* Base styles for the sidebar and section */
.wrapper {
  display: flex;
}

.sidebar {
  width: 250px;
  background-color: #f4f4f4;
  padding: 20px;
}

.section {
  flex-grow: 1;
  padding: 20px 20px 15px;
}

.sidebar {
  width: 250px;
  /* other styles */
}

/* Section */
.section {
  margin-left: 250px; /* Same width as sidebar */
  /* other styles */
}

/* Media queries for responsiveness */

/* Tablets and smaller screens */
@media (max-width: 768px) {
  /* Sidebar */
  .sidebar {
    width: 100%;
    /* other styles for smaller screens */
  }

  /* Section */
  .section {
    margin-left: 0; /* No margin on smaller screens */
    /* other styles for smaller screens */
  }
}

/* Mobile devices */
@media (max-width: 480px) {
  /* Adjust styles for smaller devices */
}
.currency-input {
  display: flex;
  align-items: center;
}

.small-select {
  width: 100px; /* Adjust the width as needed */
  margin-right: 10px; /* Add spacing between select and input */
}

.large-input {
  flex-grow: 1; /* Take remaining space */
  width: auto; /* Let it grow based on content */
}

.multi-select-dropdown {
  position: relative;
  display: inline-block;
  margin: 10px;
}

.multi-select-btn {
  background-color: #4caf50;
  color: white;
  padding: 16px;
  font-size: 16px;
  border: none;
  cursor: pointer;
}

.multi-select-btn:hover,
.multi-select-btn:focus {
  background-color: #3e8e41;
}

.multi-select-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  min-width: 200px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  padding: 10px;
}

.multi-select-content label {
  display: block;
  padding: 8px;
  cursor: pointer;
}

.multi-select-content input[type="checkbox"] {
  margin-right: 10px;
}

#submits {
  /* Adjust styles for the submit button container */
  /* position: fixed; */
  /* bottom: 10px; */
  /* margin-left: 20%; */
  /* height: 10px; */
  /* right: 10px; */
  /* Add other styles as needed */
}
/* 
@media screen and (max-height: 680px) and (min-width: 1000px) {
  .jobassigncont {
    height: 76vh !important;
  }

  .forms_JA {
    height: 71vh !important;
    overflow: auto;
  }
}  sdfjnfsdvsfvnskfdjs */

/* multi select recuritr drop down */
.multiSelectContainer {
  position: relative;
  text-align: left;
  width: 100%;
}

.searchWrapper {
  border: 1px solid #aaa !important;
  border-radius: 4px;
  min-height: 10px !important;
  padding: 3px !important ;
  position: absolute !important;
  margin-top: 0px;
  width: 100%;
  display: flex;
  overflow-x: auto; /* Enable horizontal scrolling */
  white-space: nowrap; 
}

.multiSelectContainer li {
  padding: 5px !important;
}
.chip {
  align-items: center;
  background: #0096fb;
  border-radius: 11px;
  color: #fff;
  display: inline-flex;
  font-size: 13px;
  line-height: 19px;
  margin-bottom: 5px;
  margin-right: 5px;
  padding: 4px 5px !important;
  white-space: nowrap;
}

.recruiter-selection {
  /* height:100px; */
  background-color: red;
}

.recruiter-selection .multiselect-container {
  position: relative;
}

.recruiter-selection .multiselect-container .multiselect-dropdown {
  width: 200px; /* Adjust width as needed */
  padding-right: 30px; /* Make space for the down arrow */
}

/* .recruiter-selection .multiselect-container::after {
  content: "▼ ";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 12px;
  color: #888;
} */

.multiSelectContainer ul {
  max-height: 136px;
  background-color: aliceblue;
}

.multiSelectContainer ul::-webkit-scrollbar {
  width: 4px;
}

.highlightOption {
  background: none !important;
  color: #000000 !important;
}
.highlightOption:hover {
  background: #0096fb !important;
  color: #fff !important;
}
/* .multiSelectContainer li:hover{
  background: #0096fb;
  color: #fff;
} */
@media screen and (max-width: 767px) {
 .group{
  flex-direction: row !important;
 } 
 .JS label{
  text-align: left;
 }
 .form-select{
  width: 330px;
 }
 .JSrecruiter{
  margin-top: 0px !important;
 }
 .detailed_job{
  text-align:left;
 }
#detailedJdInput{
  margin-top: 30px;
}
#detailedJdInput label{
  text-align: left;
}
 #submits_addjob{
  display: flex !important;
  justify-content: center !important;
  align-items: center !important; 
 }
}

@media screen and  (max-width: 542px) {
 
  .jobassigncont  {
    max-height: 70vh !important;
  }
 .headingtwo{
  font-size: 20px !important;
  align-items: center;
 }
 .jobassighead{
  display: flex;
  align-items: center;
  justify-content: center;
 }
 .searchWrapper {

  padding: 4px !important;

}
.chip{
  margin-top: 5px !important;
}
}



/* genie effect */

.genie-effect {
  animation: genie 0.8s ease-in-out;
  transform-origin: top left; /* important: start scaling from top-left corner */
}

@keyframes genie {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
