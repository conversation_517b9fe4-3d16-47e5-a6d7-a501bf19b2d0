import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  datesSelected:[],
  updateddateSelected:[],
  namesSelected:[],
  jobIdsSelected:[],
  emailsSelected:[],
  mobilesSelected:[],
  clientsSelected:[],
  profilesSelected:[],
  skillsSelected:[],
  recruitersSelected:[],
  statussSelected:[]
};

const filterSlice = createSlice({
  name: "dashboard data",
  initialState,
  reducers: {
    setDatesSelected: (state, action) => {
      state.datesSelected = action.payload.data;
    },
      setUpdatedDatesSelected: (state, action) => {
      state.updateddateSelected = action.payload.data;
    },
    setNamesSelected: (state, action) => {
      state.namesSelected = action.payload.data;
    },
    setjobIdsSelected: (state, action) => {
      state.jobIdsSelected = action.payload.data;
    },
    setEmailsSelected: (state, action) => {
      state.emailsSelected = action.payload.data;
    },
    setMobilesSelected: (state, action) => {
      state.mobilesSelected = action.payload.data;
    },
    setclientsSelected: (state, action) => {
      state.clientsSelected = action.payload.data;
    },
    setprofilesSelected: (state, action) => {
      state.profilesSelected = action.payload.data;
    },
    setskillsSelected: (state, action) => {
      state.skillsSelected = action.payload.data;
    },
    setrecruitersSelected: (state, action) => {
      state.recruitersSelected = action.payload.data;
    },
    setstatussSelected: (state, action) => {
      state.statussSelected = action.payload.data;
    },
  },
});

export const {setDatesSelected,setUpdatedDatesSelected,setNamesSelected,setjobIdsSelected,setEmailsSelected,setMobilesSelected,setclientsSelected,setprofilesSelected,setskillsSelected ,setrecruitersSelected,setstatussSelected} = filterSlice.actions;
export default filterSlice.reducer;
