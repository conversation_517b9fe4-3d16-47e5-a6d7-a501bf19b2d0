#pf_form {
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);

  border-radius: 5px;
  padding: 20px;
  /* width: 25rem; */
  width: 89%;
  margin: 10px auto;
  display: flex;
  /* align-items: center;
   justify-content: center; */
  flex-direction: column;
  gap: 10px;
  height: auto;
  max-height: 90%;
  overflow: auto;
  /* flex:1; */
  /* max-height: 650px; */
  /* margin-top: 10px; */
}

label {
  font-size: 16px;
  font-weight: 550;
  /* font-weight: 500; */
  color: #2e2e2e;
  /* margin-bottom: 0.1rem; */
}

.select-candidates {
  display: flex;
  flex-direction: column;
}

.btn2 {
  /* display: flex;
    align-items: center;
    justify-content: center; */
  height: 30px;
  /* max-width: 350px; */
  width: 170px;
  padding: 5px;
  border: none;
  outline: none;
  color: #fff;
  border-radius: 5px;
  margin: 25px 0;
  margin-left: 45%;
  background-color: #32406d;
  transition: all 0.3s linear;
  cursor: pointer;

  text-decoration: none;
  margin-top: 15px;
  /* Added text-decoration for the link style */
}

.btn2:hover {
  background-color: #555;
  text-decoration: none;
  /* Added text-decoration for the link style */
}

/* Animation Styles */
.fade-in-element {
  opacity: 0;
  animation: fade-in 0.5s forwards;
}

.slide-in-element {
  opacity: 0;
  transform: translateX(-10px);
  animation: slide-in 0.5s forwards;
}

.row-animation-delay:nth-child(odd) {
  animation-delay: 0.1s;
}

.row-animation-delay:nth-child(even) {
  animation-delay: 0.3s;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes slide-in {
  0% {
    opacity: 0;
    transform: translateX(-10px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive styles */

@media (max-width: 991px) {
  .section {
    margin-left: 0;
  }

  .sidebar {
    left: -225px;
  }
}

@media (max-width: 767px) {
  .top_navbar {
    padding: 0 15px;
  }

  .top_navbar .heading {
    margin-left: 10px;
  }

  .top_navbar .heading h1 {
    font-size: 16px;
  }

  .container {
    margin-top: 130px;
    /* Adjust margin to avoid overlapping with top navbar */
  }
}







/* ::-webkit-scrollbar {
  background: rgba(255, 255, 255, 0.25);
}

::-webkit-scrollbar-thumb {
  background-color: #42404034;
  border-radius: 10px;
} */

/* notification Badge */
.badge {
  position: absolute;
  top: -10px;
  right: -2px;
  padding: 5px 10px;
  border-radius: 50%;
  background: red;
  color: white;
}

/* Datatable */
table.dataTable>thead .sorting::before,
table.dataTable>thead .sorting_asc::before,
table.dataTable>thead .sorting_desc::before,
table.dataTable>thead .sorting_asc_disabled::before,
table.dataTable>thead .sorting_desc_disabled::before {
  right: 0 !important;
  content: "" !important;
}

table.dataTable>thead .sorting::after,
table.dataTable>thead .sorting_asc::after,
table.dataTable>thead .sorting_desc::after,
table.dataTable>thead .sorting_asc_disabled::after,
table.dataTable>thead .sorting_desc_disabled::after {
  right: 0 !important;
  content: "" !important;
}

#recruiter_dropdown{
  position: absolute;
  top: 80%;
  max-height: 250px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid rgb(204, 204, 204);
  transform: translate(0%);
  opacity: 4;
  z-index: 3;
  width: 30%;
  margin-left: 142px;
}
#recruiter2_dropdown{
  position: relative;
  top: 25px;
   max-height: 150px;
   overflow-y: auto;
   transform: translate(0%);
   opacity: 4;
   background-color: white;
   border: 1px solid rgb(204, 204, 204);
   z-index: 3;
   width: 30%;
   margin-left: -30%;
}
 
#selected_recruiter_id,
#search_recruiter,
#search2_recruiter,
#assign_recruiter_id {
  outline: none;
  font-size: 12px;
  font-weight: 400;
  color: #000;
  border-radius: 5px;
  border: 1px solid #aaa;
  padding: 15px;
  height: 30px;
  width: 30%;
  margin: 4px 0;
  margin-top: -5px;
  margin-left: 10px;
  background-color: white;
}
 
.pth5 {
  display: flex;
  padding-top: 5px;
  text-align: center;
  justify-content: center;
  color: #000000;
  margin-top: 30px;
  font-size: 18px;
  font-weight: 700;
}

.candidates {
  margin-bottom: 5px;
}

.candidates span {
  margin-left: 10px;
}

.profiledata {
  overflow-y: auto;
  height: auto;
  max-height: 350px;
}

@media (max-height: 680px) {
  /* #pf_form {
    height: auto;
    max-height: 500px;
  } */
}

.pf_td {
  height: 30px !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  color: #2e2e2e !important;
  padding: 5px !important;
}

@media screen and (max-width: 767px) {
  #selected_recruiter_id {
    width: 180px;
  }

  label[for="selected_recruiter_id"] {
    font-size: 14px;
    white-space: nowrap;
  }

  .spanpf {
    padding-top: 10px;
    padding-left: 5px;
    color: green;
    font-size: 14px;
  }

  .pfcontent {
    margin-top: 5px !important;
    margin-left: 15px !important;
  }

  .table-container {
    margin-top: 20px !important;
  }

  #assign_recruiter_id {
    width: 195px !important;
  }

  .pf_button {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-top: 10px;
  }

  .searchpf {
    width: 200px !important;
  }

  #pf_form {
    max-height: 750px !important;
  }
}

@media screen and (min-width:320px) and (max-width:375px) {
  #pf_form {
    width: 100% !important;
    height: 78vh !important;
  }
  .pfsearch{
    width: 150px !important;
  }
  #selected_recruiter_id {
    width: 120px !important;
  }
  .pfcontent {
    margin-top: -10px !important;
  }
  .table-container {
    margin-top: 0px !important;
  }
  #assign_recruiter_id {
    width: 188px !important;
  }
}

@media screen and (min-width:375px) and (max-width:425px) {
  .pfsearch{
    width: 170px !important;
  }
  #selected_recruiter_id {
    width: 170px !important;
  }
}


@media (max-width: 575px) {
  .container {
    margin-top: 150px;
    /* Adjust margin to avoid overlapping with top navbar */
  }

  h3 {
    font-size: 20px;
  }

  .logo {
    margin-left: 10px;
  }

  #recruiter_dropdown{
    width: 100% !important;
    margin-left: 20px ;
  }
  #recruiter2_dropdown{
    width: 100% ;
    margin-left: -150px ;}
    
      #search2_recruiter, #search_recruiter{
        width: 100% !important;
       }

  /* h1 {
    } */
}


/* new css for profile transfering */

#pf_form {
  transition: height 0.3s ease-in-out;
}

#pf_form:focus-within {
  height: auto; /* Adjust the height as needed */
}



.table .profi tr:nth-child(odd) {
  background-color: #f9f9f9 !important;
}

#candidates-table td:first-child {
  position: sticky;
  left: 0;
  background-color: transparent;
  z-index: 0;
}