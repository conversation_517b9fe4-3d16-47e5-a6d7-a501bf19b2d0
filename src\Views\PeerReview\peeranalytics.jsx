import React from 'react';
import LeftNav from "../../Components/LeftNav";
import "../../Components/leftnav.css";
import TitleBar from "../../Components/TitleBar";
import "../../Components/titlenav.css";
function PeerAnalytics() {
    return (
        <div className="wrapper">
            <LeftNav />
            <div className="section" style={{ padding: "20px 0px" }}>
                <TitleBar />
                <div>
                    <iframe title="Live_ATS"
                        width="101%"
                        height="685"
                        src="https://app.powerbi.com/groups/me/reports/29012509-d7be-4d19-8e9f-bb57be146f24/8edec991004a0704ae8a?ctid=8a7c6498-6635-4dbc-8a5e-f38efccfef3e&experience=power-bi"
                        frameborder="0"
                        allowFullScreen={true}
                        style={{ border: 'none',marginTop:"30px",marginLeft:"-5px",}}>

                    </iframe>
                </div>
            </div>
        </div>
    )
}
export default PeerAnalytics;