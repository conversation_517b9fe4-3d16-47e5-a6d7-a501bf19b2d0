{"name": "ats", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fortawesome/fontawesome-free": "^6.6.0", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-date-pickers": "^8.9.0", "@nivo/bar": "^0.87.0", "@nivo/core": "^0.87.0", "@reduxjs/toolkit": "^2.2.5", "axios": "^1.6.8", "bootstrap": "^5.3.3", "chart.js": "^4.4.3", "chartist": "^0.10.1", "cookies": "^0.9.1", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "docxtemplater": "^3.48.0", "export-to-csv": "^1.4.0", "file-saver": "^2.0.5", "framer-motion": "^11.2.4", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "i": "^0.3.7", "install": "^0.13.0", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "jszip-utils": "^0.1.0", "libphonenumber-js": "^1.12.10", "list": "^2.0.19", "material-react-table": "^3.2.1", "moment": "^2.30.1", "multiselect-react-dropdown": "^2.0.25", "npm": "^10.8.2", "pizzip": "^3.1.7", "plotly.js": "^2.34.0", "prettier": "^3.3.0", "prop-types": "^15.8.1", "react": "^18.3.1", "react-big-calendar": "^1.13.2", "react-calendar": "^5.0.0", "react-chartist": "^0.14.4", "react-chartjs-2": "^5.2.0", "react-chat-widget": "^3.1.4", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.2.0", "react-easy-crop": "^5.0.7", "react-gauge-chart": "^0.5.1", "react-heatmap-grid": "^0.9.0", "react-icons": "^5.2.1", "react-image-crop": "^11.0.5", "react-loader-spinner": "^6.1.6", "react-modal": "^3.16.1", "react-phone-input-2": "^2.15.1", "react-plotly.js": "^2.6.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.0", "react-select": "^5.10.2", "react-toastify": "^10.0.5", "react-tooltip": "^5.27.1", "react-vertical-timeline-component": "^3.6.0", "recharts": "^2.12.7", "universal": "^0.1.1", "universal-cookie": "^7.1.4", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "vite": "^5.2.0"}}