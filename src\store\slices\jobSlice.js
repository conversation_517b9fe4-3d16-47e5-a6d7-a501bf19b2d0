import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  // activeJobs:[],
  // holdJobs:[],
  jobs: [],
};

const jobSlice = createSlice({
  name: "jobs data",
  initialState,
  reducers: {
    setActiveJobs: (state, action) => {
      state.activeJobs = action.payload.jobs;
    },
    setHoldJobs: (state, action) => {
      state.holdJobs = action.payload.jobs;
    },
    setAllJobs: (state, action) => {
      state.jobs = action.payload.jobs;
    },
    updateSingleJob: (state, action) => {
      const { jobId, updatedJobData } = action.payload;
      const jobIndex = state.jobs.findIndex(job => job.id === jobId);
      if (jobIndex !== -1) {
        // Update all fields except jd_pdf and pdfs
        const { jd_pdf, pdfs, ...fieldsToUpdate } = updatedJobData;
        state.jobs[jobIndex] = {
          ...state.jobs[jobIndex],
          ...fieldsToUpdate
        };
      }
    },
  },
});
export const { setAllJobs, updateSingleJob } = jobSlice.actions;
export default jobSlice.reducer;
