import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import "./leftnav.css";

// Custom Tick for Wrapping Words on X-Axis
const CustomTick = ({ x, y, payload }) => {
  const skillName = payload.value;

  // Split the label into words to break it into multiple lines if it's too long
  const words = skillName.split(' ');
  const lines = [];
  let currentLine = '';
  words.forEach(word => {
    if (currentLine.length + word.length + 1 > 15) {
      lines.push(currentLine);
      currentLine = word;
    } else {
      currentLine += (currentLine ? ' ' : '') + word;
    }
  });
  if (currentLine) {
    lines.push(currentLine);
  }

  return (
    <g transform={`translate(${x},${y})`} style={{ overflow: 'visible' }}>
      {lines.map((line, index) => (
        <text
          key={index}
          x={0}
          y={index * 15} // Adjust spacing between lines
          textAnchor="middle"
          fontSize="12px" // Adjust font size if needed
          fill="#32406d"  // Change color if needed
        >
          {line}
        </text>
      ))}
    </g>
  );
};

const BarChartComponent = ({ categoriesCounts, setClickedData }) => {
  const data = categoriesCounts?.map(item => ({
    name: item.category,
    count: item.count,
    items: item.items, // Assuming 'items' is an array
  }));

  // Calculate dynamic chart width based on data length
  const chartWidth = Math.max(data.length * 100, 800); // Adjust per label size

  return (
    <div style={{ width: '100%' }}> {/* Add horizontal scroll */}
      <ResponsiveContainer width={chartWidth} height={450} className="expertise">
        <BarChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 70, // Increased bottom margin for longer labels
          }}
          barSize={30}
          onClick={(e) => {
            const { activePayload } = e;
            if (activePayload && activePayload.length > 0) {
              const clickedItem = categoriesCounts.find(
                item => item.category === activePayload[0].payload.name
              );
              setClickedData(clickedItem);
              console.log('Clicked Data:', clickedItem);
            }
          }}
        >
          <CartesianGrid strokeDasharray="2 2" />
          <XAxis
            dataKey="name"
            scale="point"
            padding={{ left: 30, right: 30,bottom: 20 }}
            margin={{bottom: 30}}
            tick={<CustomTick />} // Apply custom tick component for word wrapping
            tickMargin={20}
            interval={0} // Ensures all labels are displayed
            label={{
              position: 'insideBottom',
              style: { fill: 'rgb(136, 132, 216)', fontSize: '16px', fontWeight: "500" },
            }}
            
          />
          <YAxis
            domain={[0, 'auto']}
            label={{
              value: 'Expertise Levels',
              angle: -90,
              position: 'insideLeft',
              offset: 10,
              style: { fill: 'rgb(136, 132, 216)', fontSize: "20px", fontWeight: "500" },
              dy: 50,
            }}
          />
          <Tooltip 
            content={({ payload }) => {
              if (payload && payload.length > 0) {
                const { count, items } = payload[0].payload;
                return (
                  <div style={{
                    backgroundColor: "rgb(255, 255, 255)",
                    border: "1px solid rgb(204, 204, 204)",
                    width: "300px",
                  }}>
                    <p style={{ color: "#000", padding: "0" }}>Count: {count}</p>
                    <p style={{ color: "#000" }}>Items: {items}</p>
                  </div>
                );
              }
              return null;
            }}
          />
          <Legend />
          <Bar dataKey="count" padding={50}  fill="rgb(136, 132, 216)"    background={{ fill: 'transparent' }} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BarChartComponent;
