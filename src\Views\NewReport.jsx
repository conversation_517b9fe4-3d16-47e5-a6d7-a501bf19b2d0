import React from 'react';
import LeftNav from "../Components/LeftNav";
import "../Components/leftnav.css";
import TitleBar from "../Components/TitleBar";
import "../Components/titlenav.css";
function NewReport() {
    return (
        <div className="wrapper">
            <LeftNav />
            <div className="section" style={{ padding: "20px 0px" }}>
                <TitleBar />
                <div>
                    <iframe title="Live_ATS"
                        width="101%"
                        height="685"
                        src="https://app.powerbi.com/reportEmbed?reportId=0a262801-4504-4342-91cc-b3771a39a397&autoAuth=true&ctid=8a7c6498-6635-4dbc-8a5e-f38efccfef3e&actionBarEnabled=false&filterPaneEnabled=false&navContentPaneEnabled=false"
                        frameborder="0"
                        allowFullScreen={true}
                        style={{ border: 'none',marginTop:"30px",marginLeft:"-5px",}}>

                    </iframe>
                </div>
            </div>
        </div>
    )
}
export default NewReport;