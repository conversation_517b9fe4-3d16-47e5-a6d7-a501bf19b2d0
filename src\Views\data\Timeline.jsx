import React from 'react';
import {
  VerticalTimeline,
  VerticalTimelineElement,
} from 'react-vertical-timeline-component';
import 'react-vertical-timeline-component/style.min.css';
import '@fortawesome/fontawesome-free/css/all.min.css';
import './Timeline.css'; // Import the CSS file

const getRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const Timeline = ({ events }) => {
  return (
    <div style={{height:"100%"}}>
      {/* <div style={{marginTop:"-30px"}}><h5 className="card-title">Career Progress of the Candidate </h5></div> */}
      <div className="timeline-container" style={{width:"100%"}}>
        <VerticalTimeline>
          {events?.map((event, index) => {
            // Generate a random color for each event
            const randomColor = getRandomColor();
            const iconClass = event.icon || 'fa-building'; 
            return (
              <VerticalTimelineElement
                key={index}
                date={event.date}
                iconStyle={{ background: randomColor, color: '#fff' }}
                contentArrowStyle={{ borderRight: '7px solid  rgb(33, 150, 243)' }}
                icon={<i className={`fas ${iconClass}`} style={{fontSize:"30px",marginLeft:"17px",marginTop:"13px"}}></i>}
              >
                <h3 className="vertical-timeline-element-title" style={{fontSize:"15px",fontWeight:"500",color:"rgb(136, 132, 216)"}}>{event.Role}</h3>
                <h4 className="vertical-timeline-element-subtitle" style={{fontSize:"15px",fontWeight:"400"}}> {event.subtitle}</h4>
                <div className="vertical-timeline-element-subtitle"><span style={{fontSize:"14px",fontWeight:"500"}}>Projects:</span> <span style={{fontSize:"13px",fontWeight:"300"}}>{event.Project}</span> </div>
                <div className="vertical-timeline-element-subtitle"><span style={{fontSize:"14px",fontWeight:"500"}}>Company:  </span><span style={{fontSize:"13px",fontWeight:"300"}}>{event.Company}</span></div>
                <div className="vertical-timeline-element-subtitle"><span style={{fontSize:"14px",fontWeight:"500"}} >Location:</span> <span style={{fontSize:"13px",fontWeight:"300"}}>{event.Location}</span></div>
                <div className="vertical-timeline-element-subtitle"><span style={{fontSize:"14px",fontWeight:"500"}}>From:</span> <span style={{fontSize:"13px",fontWeight:"300"}}> {event.FromDate}</span></div>
                <div className="vertical-timeline-element-subtitle"><span style={{fontSize:"14px",fontWeight:"500"}}>To:</span> <span style={{fontSize:"13px",fontWeight:"300"}}>{event.ToDate}</span></div>
                {/* <div className="vertical-timeline-element-subtitle">Company:<br/>{event.duration}</div> */}
                {/* <p>{event.description}</p> */}
              </VerticalTimelineElement>
            );
          })}
        </VerticalTimeline>
      </div>
    </div>
  );
};

export default Timeline;
