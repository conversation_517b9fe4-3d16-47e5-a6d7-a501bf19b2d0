import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  peerReviewData: {},
  candidatesPendingApproval: [],
  loading: false,
  error: null,
};

const peerReviewSlice = createSlice({
  name: "peer review data",
  initialState,
  reducers: {
    setPeerReviewData: (state, action) => {
      state.peerReviewData = action.payload.data;
      state.candidatesPendingApproval = action.payload.data.candidates || [];
      state.loading = false;
      state.error = null;
    },
    setPeerReviewLoading: (state, action) => {
      state.loading = action.payload;
    },
    setPeerReviewError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    },
    clearPeerReviewData: (state) => {
      state.peerReviewData = {};
      state.candidatesPendingApproval = [];
      state.loading = false;
      state.error = null;
    },
  },
});

export const { 
  setPeerReviewData, 
  setPeerReviewLoading, 
  setPeerReviewError, 
  clearPeerReviewData 
} = peerReviewSlice.actions;

export default peerReviewSlice.reducer;
