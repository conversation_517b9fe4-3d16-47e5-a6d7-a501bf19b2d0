/* changepassword.css */

/* body {
    font-family: 'poppins', sans-serif;
    background: #cad1ff;
    background-size: cover;
    background-origin: border-box;
    background-position: center;
    background-attachment: fixed;
  } */

h5 {
  display: flex;
  padding-top: 5px;
  text-align: center;
  justify-content: center;
  color: #000000;
}

.form {
  max-width: 400px;
  margin: 0 auto;
  padding: 0 25px;
  background: rgba(255, 255, 255, 0.25);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(11.5px);
  -webkit-backdrop-filter: blur(11.5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  margin-top: 50px;
  height: auto; /* Adjusted to auto height */
  position: relative; /* Added position relative */
}

/* .label_cp {
    display: block;
    margin-bottom: 15px; 
    font-weight: bold;
    font-size: 15px;
    margin-top: 10px;
  } */

/* #old_password,
  #new_password,
  #confirm_password,
  #username {
    width: 100%;
    padding: 10px;
    margin-bottom: 10px; 
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(11.5px);
    -webkit-backdrop-filter: blur(11.5px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 3px;
    box-sizing: border-box;
    height: 40px; 
  } */

.Change_password {
  width: 100%;
  padding: 10px;
  background-color: #32406d;
  color: #fff;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  height: 40px;
  margin-bottom: 10px; /* Added margin-bottom */
}

.Change_password:hover {
  background-color: #555;
}

.error {
  color: red;
  margin-top: 5px; /* Added margin-top for error messages */
  position: relative; /* Absolute positioning */
  left: 0;
}

/* For mobile screens */
@media screen and (min-width: 767px) {
  .form {
    max-width: 23rem;
  }
  .form-input{
    width: 340px !important;
  }
  .show-passwords{
    right: 54px !important;
  }

  /* Other mobile-specific styles if needed */
}
#error_old_password {
  top: 80%;
  left: 10px;
}
#error_new_password {
  top: 80%;
  left: 10px;
}
#error_confirm_password {
  top: 82%;
  left: 10px;
}
#error_username {
  top: 80%;
  left: 10px;
}
.form-group_cp {
  position: relative;
  margin: 20px auto;
}
@media screen and (max-width: 767px) {
  label[for="confirm_password"]{
    text-align: left;
  }
  label[for="new_password"]{
    text-align: left;
  }
  label[for="old_password"]{
    text-align: left;
  }
  label[for="username"]{
    text-align: left;
  }
}

@media screen and (min-width:320px) and (max-width:375px) {
  .show-passwords{
    margin-right:40px !important;
    margin-top: 0px !important;
  }
  .containeracco{
    height: 450px !important;
  }
  .error {
    color: red;
    font-size: 12px;
  }
  .signup-content{
    height: 450px !important;
  }
}
@media screen and (min-width:375px) and (max-width:425px){
  .show-passwords{
    margin-right:40px !important;
    margin-top: 0px !important;
  }
  .containeracco{
    height: 450px !important;
  }
  .error {
    color: red;
    font-size: 12px;
  }
  .signup-content{
    height: 450px !important;
  }
}